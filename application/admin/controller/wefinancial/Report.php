<?php

namespace app\admin\controller\wefinancial;

use app\admin\model\wefinancial\Period;
use app\admin\model\wefinancial\PeriodBalance;
use app\admin\model\wefinancial\ReportItem;
use app\admin\model\wefinancial\ReportTemplate;
use app\admin\model\wefinancial\ReportColumn;
use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 报表
 *
 * @icon fa fa-circle-o
 */
class Report extends Backend
{
    protected $model = null;
    protected $reportTemplateModel = null;
    protected $reportItemModel = null;
    protected $reportColumnModel = null;
    protected $periodBalanceModel = null;
    protected $periodModel = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->reportTemplateModel = new ReportTemplate;
        $this->reportItemModel = new ReportItem;
        $this->reportColumnModel = new ReportColumn;
        $this->periodBalanceModel = new PeriodBalance;
        $this->periodModel = new Period;
        $this->assign('companyName', get_addon_config('wefinancial')['company_name']);
    }

    /**
     * 报表列表页面
     */
    public function index()
    {
        // 获取可用的报表模板
        $templates = $this->reportTemplateModel
            ->where('status', 1)
            ->field('id,name,type,description')
            ->order('id', 'asc')
            ->select();

        // 传递数据到模板
        $this->view->assign([
            'templates' => $templates
        ]);

        return $this->view->fetch();
    }

    /**
     * 获取期间列表
     */
    public function getPeriods()
    {
        if ($this->request->isAjax()) {
            // 获取可用的会计期间
            $periods = $this->periodModel
                ->field('period')
                ->order('period', 'desc')
                ->select();

            // 获取当前期间
            $currentPeriod = $this->periodBalanceModel->getCurrentPeriod();

            $this->success('获取成功', null, [
                'periods' => $periods,
                'currentPeriod' => $currentPeriod
            ]);
        }
    }

    /**
     * 报表详情页面
     */
    public function detail()
    {
        $templateId = $this->request->param('template_id');
        $period = $this->request->param('period');

        if (!$templateId) {
            $this->error('参数错误');
        }

        // 获取报表模板信息
        $template = $this->reportTemplateModel->find($templateId);
        if (!$template) {
            $this->error('报表模板不存在');
        }

        // 如果没有传入期间，使用当前期间
        if (!$period) {
            $period = $this->periodBalanceModel->getCurrentPeriod();
        }

        // 获取可用的会计期间列表
        $periods = $this->periodModel
            ->field('period')
            ->order('period', 'desc')
            ->select();

        // 生成报表数据
        $reportData = $this->generateReportData($templateId, $period);

        // 获取动态列定义
        $columns = $this->reportColumnModel
            ->where('template_id', $templateId)
            ->where('status', 1)
            ->order('column_position', 'asc')
            ->select();

        // 获取公司名称
        $companyName = get_addon_config('wefinancial')['company_name'] ?? '未设置';

        // 基础数据传递
        $assignData = [
            'template' => $template,
            'period' => $period,
            'periods' => $periods,
            'reportData' => $reportData,
            'columns' => $columns,
            'companyName' => $companyName
        ];

        // 只有双栏报表才需要预处理模板数据
        if ($this->isDoubleColumnReport($template->id)) {
            $assignData['templateData'] = $this->prepareTemplateData($template, $reportData, $columns);
        }

        // 传递数据到模板
        $this->view->assign($assignData);

        return $this->view->fetch();
    }

    /**
     * 导出Excel
     */
    public function export()
    {
        $templateId = $this->request->param('template_id');
        $period = $this->request->param('period');

        if (!$templateId || !$period) {
            $this->error('参数错误');
        }

        // 获取报表模板信息
        $template = $this->reportTemplateModel->find($templateId);
        if (!$template) {
            $this->error('报表模板不存在');
        }

        // 获取报表数据
        $reportData = $this->generateReportData($templateId, $period);

        // 获取动态列定义
        $columns = $this->reportColumnModel
            ->where('template_id', $templateId)
            ->where('status', 1)
            ->order('column_position', 'asc')
            ->select();

        // 获取公司名称
        $companyName = config('site.name') ?: '未设置';

        // 生成Excel文件
        $this->generateExcel($template, $period, $reportData, $columns, $companyName);
    }

    /**
     * 获取报表项目的科目关联
     */
    public function getItemSubjects()
    {
        $itemId = $this->request->param('item_id');

        if (!$itemId) {
            $this->error('参数错误');
        }

        // 获取科目关联数据
        $subjects = \think\Db::name('wefinancial_report_item_subject')
            ->alias('ris')
            ->join('wefinancial_subject s', 'ris.subject_id = s.id')
            ->where('ris.item_id', $itemId)
            ->where('ris.status', 1)
            ->field('ris.*, s.code, s.name as subject_name, s.balance_direction as subject_balance_direction')
            ->order('ris.id', 'asc')
            ->select();

        $this->success('获取成功', null, $subjects);
    }

    /**
     * 保存报表项目的科目关联
     */
    public function saveItemSubjects()
    {
        $itemId = $this->request->param('item_id');
        $subjects = $this->request->param('subjects/a', []);

        if (!$itemId) {
            $this->error('参数错误');
        }

        \think\Db::startTrans();
        try {
            // 删除原有关联
            \think\Db::name('wefinancial_report_item_subject')
                ->where('item_id', $itemId)
                ->delete();

            // 添加新关联
            if (!empty($subjects)) {
                $insertData = [];
                foreach ($subjects as $subject) {
                    $insertData[] = [
                        'item_id' => $itemId,
                        'subject_id' => $subject['subject_id'],
                        'operation_type' => $subject['operation_type'],
                        'balance_direction' => $subject['balance_direction'],
                        'weight' => $subject['weight'] ?: 1.0000,
                        'status' => 1,
                        'createtime' => time(),
                        'updatetime' => time()
                    ];
                }
                \think\Db::name('wefinancial_report_item_subject')->insertAll($insertData);
            }

            \think\Db::commit();
        } catch (\Exception $e) {
            \think\Db::rollback();
            $this->error('保存失败：' . $e->getMessage());
        }
        $this->success('保存成功');
    }

    public function test()
    {
        return $this->fetch();
    }

    /**
     * 获取科目配置弹窗HTML
     */
    public function subjectConfig()
    {
        $itemId = $this->request->param('item_id');

        if (!$itemId) {
            $this->error('参数错误');
        }

        // 获取科目关联数据
        $subjects = \think\Db::name('wefinancial_report_item_subject')
            ->alias('ris')
            ->join('wefinancial_subject s', 'ris.subject_id = s.id')
            ->where('ris.item_id', $itemId)
            ->where('ris.status', 1)
            ->field('ris.*, s.code, s.name as subject_name, s.balance_direction as subject_balance_direction')
            ->order('ris.id', 'asc')
            ->select();

        // 传递数据到模板
        $this->view->assign([
            'itemId' => $itemId,
            'subjects' => $subjects
        ]);

        return $this->fetch();
    }

    /**
     * 获取科目树形数据
     */
    public function getSubjectTree()
    {
        $subjects = \think\Db::name('wefinancial_subject')
            ->where('status', 1)
            ->field('id, code, name, pid, level, balance_direction, leaf_flag')
            ->order('code', 'asc')
            ->select();

        // 构建树形结构
        $tree = [];
        $subjectMap = [];

        foreach ($subjects as $subject) {
            $subjectMap[$subject['id']] = $subject;
            $subject['children'] = [];
            $subject['text'] = $subject['code'] . ' ' . $subject['name'];
            $subject['selectable'] = $subject['leaf_flag'] == 1; // 只有末级科目可选

            if ($subject['pid'] == 0) {
                $tree[] = $subject;
            }
        }

        // 构建父子关系
        foreach ($subjects as $subject) {
            if ($subject['pid'] > 0 && isset($subjectMap[$subject['pid']])) {
                $subjectMap[$subject['pid']]['children'][] = $subject;
            }
        }

        $this->success('获取成功', null, $tree);
    }

    /**
     * 生成Excel文件
     */
    private function generateExcel($template, $period, $reportData, $columns, $companyName)
    {
        // 这里使用简单的HTML表格导出方式
        // 在实际项目中可以使用PHPExcel或PhpSpreadsheet库

        $filename = $template->type . '_' . $period . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<html><head><meta charset="utf-8"></head><body>';

        // 打印头部
        echo '<div style="text-align: center; margin-bottom: 20px;">';
        echo '<h3 style="margin: 0 0 15px 0; font-weight: bold;">' . $template->type . '</h3>';
        echo '<table width="100%" style="margin-bottom: 15px;">';
        echo '<tr>';
        echo '<td style="text-align: left;">编制单位：' . $companyName . '</td>';
        echo '<td style="text-align: center;">报表期间：' . $period . '</td>';
        echo '<td style="text-align: right;">单位：元</td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // 报表内容
        if ($this->isDoubleColumnReport($template->id)) {
            $this->generateBalanceSheetExcel($reportData, $columns);
        } else {
            $this->generateSingleColumnExcel($reportData, $columns);
        }

        echo '</body></html>';
        exit;
    }

    /**
     * 生成双栏报表Excel
     */
    private function generateBalanceSheetExcel($reportData, $columns)
    {
        $leftItems = array_filter($reportData, function ($item) {
            return $item['column_position'] === 'left';
        });
        $rightItems = array_filter($reportData, function ($item) {
            return $item['column_position'] === 'right';
        });

        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<thead>';
        echo '<tr>';

        // 左侧资产部分
        echo '<th style="width: 20%; text-align: center; background-color: #f8f9fa;">项目</th>';
        echo '<th style="width: 5%; text-align: center; background-color: #f8f9fa;">行次</th>';

        // 动态列头 - 左侧
        $columnWidth = count($columns) > 0 ? (20 / count($columns)) : 10;
        foreach ($columns as $column) {
            echo '<th style="width: ' . $columnWidth . '%; text-align: center; background-color: #f8f9fa;">' . $column->column_name . '</th>';
        }

        // 右侧负债和权益部分
        echo '<th style="width: 20%; text-align: center; background-color: #f8f9fa;">项目</th>';
        echo '<th style="width: 5%; text-align: center; background-color: #f8f9fa;">行次</th>';

        // 动态列头 - 右侧
        foreach ($columns as $column) {
            echo '<th style="width: ' . $columnWidth . '%; text-align: center; background-color: #f8f9fa;">' . $column->column_name . '</th>';
        }

        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        $maxRows = max(count($leftItems), count($rightItems));
        $leftItems = array_values($leftItems);
        $rightItems = array_values($rightItems);

        for ($i = 0; $i < $maxRows; $i++) {
            $leftItem = isset($leftItems[$i]) ? $leftItems[$i] : [];
            $rightItem = isset($rightItems[$i]) ? $rightItems[$i] : [];

            echo '<tr>';

            // 左侧资产
            if (!empty($leftItem)) {
                $leftIndent = str_repeat('&nbsp;', $leftItem['indent'] * 4);
                $leftStyle = $leftItem['bold'] ? 'font-weight: bold;' : '';
                echo '<td style="' . $leftStyle . '">' . $leftIndent . $leftItem['item_name'] . '</td>';
                echo '<td style="text-align: center;">' . ($leftItem['line_number'] ?: '') . '</td>';

                // 动态列数据 - 左侧
                foreach ($columns as $column) {
                    $columnKey = 'column_' . $column->id;
                    $amount = isset($leftItem[$columnKey]) ? $leftItem[$columnKey] : 0;
                    $formattedAmount = number_format($amount, $column->decimal_places);
                    echo '<td style="text-align: right;">' . $formattedAmount . '</td>';
                }
            } else {
                // 空行 - 左侧
                echo '<td></td><td></td>';
                foreach ($columns as $column) {
                    echo '<td></td>';
                }
            }

            // 右侧负债和权益
            if (!empty($rightItem)) {
                $rightIndent = str_repeat('&nbsp;', $rightItem['indent'] * 4);
                $rightStyle = $rightItem['bold'] ? 'font-weight: bold;' : '';
                echo '<td style="' . $rightStyle . '">' . $rightIndent . $rightItem['item_name'] . '</td>';
                echo '<td style="text-align: center;">' . ($rightItem['line_number'] ?: '') . '</td>';

                // 动态列数据 - 右侧
                foreach ($columns as $column) {
                    $columnKey = 'column_' . $column->id;
                    $amount = isset($rightItem[$columnKey]) ? $rightItem[$columnKey] : 0;
                    $formattedAmount = number_format($amount, $column->decimal_places);
                    echo '<td style="text-align: right;">' . $formattedAmount . '</td>';
                }
            } else {
                // 空行 - 右侧
                echo '<td></td><td></td>';
                foreach ($columns as $column) {
                    echo '<td></td>';
                }
            }

            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }

    /**
     * 生成单栏报表Excel
     */
    private function generateSingleColumnExcel($reportData, $columns)
    {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<thead>';
        echo '<tr>';
        echo '<th style="width: 50%; text-align: center; background-color: #f8f9fa;">项目</th>';
        echo '<th style="width: 10%; text-align: center; background-color: #f8f9fa;">行次</th>';

        // 动态生成列头
        $columnWidth = count($columns) > 0 ? (40 / count($columns)) : 20;
        foreach ($columns as $column) {
            echo '<th style="width: ' . $columnWidth . '%; text-align: center; background-color: #f8f9fa;">' . $column->column_name . '</th>';
        }

        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($reportData as $item) {
            $indent = str_repeat('&nbsp;', $item['indent'] * 4);
            $style = $item['bold'] ? 'font-weight: bold;' : '';

            echo '<tr>';
            echo '<td style="' . $style . '">' . $indent . $item['item_name'] . '</td>';
            echo '<td style="text-align: center;">' . ($item['line_number'] ?: '') . '</td>';

            // 动态生成数据列
            foreach ($columns as $column) {
                $columnKey = 'column_' . $column->id;
                $amount = isset($item[$columnKey]) ? $item[$columnKey] : 0;
                $formattedAmount = number_format($amount, $column->decimal_places);
                echo '<td style="text-align: right;">' . $formattedAmount . '</td>';
            }

            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }

    /**
     * 生成报表数据（优化版本，增强内存管理）
     * @param int $templateId 报表模板ID
     * @param string $period 会计期间
     * @return array
     */
    private function generateReportData($templateId, $period)
    {
        // 检查内存使用情况
        $memoryLimit = $this->getMemoryLimit();
        $currentMemory = memory_get_usage(true);

        if ($currentMemory > $memoryLimit * 0.8) {
            throw new \Exception('内存使用过高，无法生成报表');
        }

        // 获取报表项目
        $items = $this->reportItemModel
            ->where('template_id', $templateId)
            ->where('status', 1)
            ->order('weigh', 'asc')
            ->select();

        // 获取动态列定义
        $columns = $this->reportColumnModel
            ->where('template_id', $templateId)
            ->where('status', 1)
            ->order('column_position', 'asc')
            ->select();

        // 预加载优化：批量获取所有需要的数据
        $this->preloadReportData($items, $period, $columns);

        $reportData = [];
        $batchSize = 20; // 分批处理，减少内存占用
        $itemBatches = array_chunk($items, $batchSize);

        foreach ($itemBatches as $batch) {
            foreach ($batch as $item) {
                $itemData = [
                    'id' => $item->id,
                    'item_name' => $item->item_name,
                    'line_number' => $item->line_number ?: '',
                    'column_position' => $item->column_position,
                    'type' => $item->type,
                    'calculation_type' => $item->calculation_type,
                    'calculation_formula' => $item->calculation_formula,
                    'bold' => $item->bold,
                    'indent' => $item->indent
                ];

                // 计算动态列数据
                foreach ($columns as $column) {
                    $columnKey = 'column_' . $column->id;

                    if ($item->calculation_type == 'direct') {
                        // 直接取数（使用缓存数据）
                        $itemData[$columnKey] = $this->calculateDirectAmountForColumnOptimized($item->id, $period, $column);
                    } elseif ($item->calculation_type == 'formula') {
                        // 公式计算 - 在所有直接取数完成后再计算
                        $itemData[$columnKey] = 0.00;
                    }
                }

                $reportData[] = $itemData;
            }

            // 检查内存使用，如果过高则强制垃圾回收
            if (memory_get_usage(true) > $memoryLimit * 0.7) {
                gc_collect_cycles();
            }
        }

        // 第二轮处理：计算公式项目（分批处理）
        $formulaItems = array_filter($reportData, function ($item) {
            return $item['calculation_type'] == 'formula';
        });

        foreach ($formulaItems as $key => $itemData) {
            foreach ($columns as $column) {
                $columnKey = 'column_' . $column->id;
                $reportData[$key][$columnKey] = $this->calculateFormulaAmountForColumn($itemData['calculation_formula'], $reportData, $column);
            }
        }

        return $reportData;
    }

    /**
     * 获取内存限制
     * @return int 内存限制（字节）
     */
    private function getMemoryLimit()
    {
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int)$memoryLimit;

        switch ($unit) {
            case 'g':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $value *= 1024 * 1024;
                break;
            case 'k':
                $value *= 1024;
                break;
        }

        return $value;
    }

    // 缓存变量
    private $subjectTreeCache = [];
    private $periodBalanceCache = [];
    private $itemSubjectsCache = [];

    /**
     * 预加载报表数据（批量优化，减少内存占用）
     * @param array $items 报表项目
     * @param string $period 会计期间
     * @param array $columns 动态列
     */
    private function preloadReportData($items, $period, $columns)
    {
        // 清空之前的缓存，释放内存
        $this->clearCache();

        // 1. 批量获取所有报表项目的科目关联
        $itemIds = array_column($items, 'id');
        $this->itemSubjectsCache = $this->batchGetItemSubjects($itemIds);

        // 2. 获取所有涉及的科目ID
        $allSubjectIds = [];
        foreach ($this->itemSubjectsCache as $itemSubjects) {
            foreach ($itemSubjects as $itemSubject) {
                $allSubjectIds[] = $itemSubject['subject_id'];
            }
        }
        $allSubjectIds = array_unique($allSubjectIds);

        // 3. 批量构建科目树结构（分批处理）
        $subjectBatches = array_chunk($allSubjectIds, 50);
        foreach ($subjectBatches as $batch) {
            $batchTree = $this->batchBuildSubjectTree($batch);
            $this->subjectTreeCache = $this->subjectTreeCache + $batchTree;
        }

        // 4. 批量获取期间余额数据（分批处理）
        $this->periodBalanceCache = $this->batchGetPeriodBalances($allSubjectIds, $period, $columns);

        // 强制垃圾回收
        gc_collect_cycles();
    }

    /**
     * 清空缓存，释放内存
     */
    private function clearCache()
    {
        $this->subjectTreeCache = [];
        $this->periodBalanceCache = [];
        $this->itemSubjectsCache = [];
        \app\admin\model\wefinancial\PeriodBalance::clearVirtualBalanceCache();
    }


    /**
     * 批量获取报表项目的科目关联
     * @param array $itemIds 报表项目ID数组
     * @return array
     */
    private function batchGetItemSubjects($itemIds)
    {
        $itemSubjects = Db::name('wefinancial_report_item_subject')
            ->whereIn('item_id', $itemIds)
            ->where('status', 1)
            ->select();

        $result = [];
        foreach ($itemSubjects as $itemSubject) {
            $result[$itemSubject['item_id']][] = $itemSubject;
        }

        return $result;
    }

    /**
     * 批量构建科目树结构
     * @param array $subjectIds 科目ID数组
     * @return array
     */
    private function batchBuildSubjectTree($subjectIds)
    {
        // 获取所有科目数据
        $subjects = \think\Db::name('wefinancial_subject')
            ->where('status', 1)
            ->field('id, pid, code, name')
            ->select();

        // 构建父子关系映射
        $childrenMap = [];
        foreach ($subjects as $subject) {
            if ($subject['pid'] > 0) {
                $childrenMap[$subject['pid']][] = $subject['id'];
            }
        }

        // 为每个科目计算其所有下级科目
        $result = [];
        foreach ($subjectIds as $subjectId) {
            $result[$subjectId] = $this->getSubjectAndChildrenIdsFromCache($subjectId, $childrenMap);
        }

        return $result;
    }

    /**
     * 从缓存中递归获取科目及其所有下级科目的ID
     * @param int $subjectId 科目ID
     * @param array $childrenMap 子科目映射
     * @return array
     */
    private function getSubjectAndChildrenIdsFromCache($subjectId, $childrenMap)
    {
        $ids = [$subjectId];

        if (isset($childrenMap[$subjectId])) {
            foreach ($childrenMap[$subjectId] as $childId) {
                $ids = array_merge($ids, $this->getSubjectAndChildrenIdsFromCache($childId, $childrenMap));
            }
        }

        return $ids;
    }


    /**
     * 批量获取期间余额数据
     * @param array $subjectIds 科目ID数组
     * @param string $period 会计期间
     * @param array $columns 动态列
     * @return array
     */
    private function batchGetPeriodBalances($subjectIds, $period, $columns)
    {
        $result = [];

        // 获取所有涉及的科目ID（包括下级科目）
        $allSubjectIds = [];
        foreach ($subjectIds as $subjectId) {
            if (isset($this->subjectTreeCache[$subjectId])) {
                $allSubjectIds = array_merge($allSubjectIds, $this->subjectTreeCache[$subjectId]);
            } else {
                $allSubjectIds[] = $subjectId;
            }
        }
        $allSubjectIds = array_unique($allSubjectIds);

        // 为每个列获取所需的期间数据
        foreach ($columns as $column) {
            $periodsToCache = $this->getPeriodsToCache($period, $column);

            foreach ($periodsToCache as $periodToCache) {
                $cacheKey = $periodToCache . '_' . $column->balance_type;

                if (!isset($result[$cacheKey])) {
                    $result[$cacheKey] = $this->batchGetVirtualBalances($allSubjectIds, $periodToCache);
                }
            }
        }

        return $result;
    }

    /**
     * 获取需要缓存的期间列表
     * @param string $period 基准期间
     * @param object $column 列定义
     * @return array
     */
    private function getPeriodsToCache($period, $column)
    {
        $periodType = $column->period_type;
        $balanceType = $column->balance_type;

        switch ($periodType) {
            case 'current_year':
                $year = substr($period, 0, 4);
                if ($balanceType == 'period') {
                    // 年度本期发生需要缓存年度内所有期间
                    $firstPeriod = $year . '-01';
                    $lastPeriod = $this->getYearLastPeriod($year);
                    return $this->getPeriodsInRange($firstPeriod, $lastPeriod);
                } else {
                    // 年度期初/期末只需要缓存特定期间
                    $targetPeriod = ($balanceType == 'beginning') ? $year . '-01' : $this->getYearLastPeriod($year);
                    return [$targetPeriod];
                }

            case 'last_year':
                $year = intval(substr($period, 0, 4)) - 1;
                if ($balanceType == 'period') {
                    // 上年度本期发生需要缓存上年度内所有期间
                    $firstPeriod = $year . '-01';
                    $lastPeriod = $this->getYearLastPeriod($year);
                    return $this->getPeriodsInRange($firstPeriod, $lastPeriod);
                } else {
                    // 上年度期初/期末只需要缓存特定期间
                    $targetPeriod = ($balanceType == 'beginning') ? $year . '-01' : $this->getYearLastPeriod($year);
                    return [$targetPeriod];
                }

            default:
                // 月度期间类型
                $actualPeriod = $this->getActualPeriod($period, $periodType);
                return [$actualPeriod];
        }
    }

    /**
     * 批量获取虚拟余额数据
     * @param array $subjectIds 科目ID数组
     * @param string $period 会计期间
     * @return array
     */
    private function batchGetVirtualBalances($subjectIds, $period)
    {
        $result = [];

        // 批量查询当期余额记录
        $currentBalances = $this->periodBalanceModel
            ->whereIn('subject_id', $subjectIds)
            ->where('period', $period)
            ->column('*', 'subject_id');

        // 批量查询历史余额记录（用于没有当期记录的科目）
        $missingSubjectIds = array_diff($subjectIds, array_keys($currentBalances));
        $historicalBalances = [];

        if (!empty($missingSubjectIds)) {
            $historicalBalances = $this->periodBalanceModel
                ->whereIn('subject_id', $missingSubjectIds)
                ->where('period', '<', $period)
                ->field('subject_id, period, end_balance, end_direction')
                ->order('subject_id, period desc')
                ->select();

            // 按科目ID分组，每个科目只保留最新的记录
            $latestBalances = [];
            foreach ($historicalBalances as $balance) {
                if (!isset($latestBalances[$balance['subject_id']])) {
                    $latestBalances[$balance['subject_id']] = $balance;
                }
            }
            $historicalBalances = $latestBalances;
        }

        // 构建结果数组
        foreach ($subjectIds as $subjectId) {
            if (isset($currentBalances[$subjectId])) {
                // 有当期记录
                $balance = $currentBalances[$subjectId];
                $result[$subjectId] = [
                    'subject_id' => $subjectId,
                    'period' => $period,
                    'begin_balance' => $balance['begin_balance'],
                    'begin_direction' => $balance['begin_direction'],
                    'debit_total' => $balance['debit_total'],
                    'credit_total' => $balance['credit_total'],
                    'end_balance' => $balance['end_balance'],
                    'end_direction' => $balance['end_direction'],
                    'has_actual_record' => true
                ];
            } elseif (isset($historicalBalances[$subjectId])) {
                // 基于历史记录构造虚拟余额
                $balance = $historicalBalances[$subjectId];
                $result[$subjectId] = [
                    'subject_id' => $subjectId,
                    'period' => $period,
                    'begin_balance' => $balance['end_balance'],
                    'begin_direction' => $balance['end_direction'],
                    'debit_total' => 0.00,
                    'credit_total' => 0.00,
                    'end_balance' => $balance['end_balance'],
                    'end_direction' => $balance['end_direction'],
                    'has_actual_record' => false,
                    'based_on_period' => $balance['period']
                ];
            } else {
                // 没有任何记录，使用科目默认方向
                $subject = \app\admin\model\wefinancial\Subject::find($subjectId);
                $defaultDirection = $subject ? $subject->balance_direction : '借';
                $result[$subjectId] = [
                    'subject_id' => $subjectId,
                    'period' => $period,
                    'begin_balance' => 0.00,
                    'begin_direction' => $defaultDirection,
                    'debit_total' => 0.00,
                    'credit_total' => 0.00,
                    'end_balance' => 0.00,
                    'end_direction' => $defaultDirection,
                    'has_actual_record' => false,
                    'based_on_period' => null
                ];
            }
        }

        return $result;
    }

    /**
     * 为指定列计算直接取数金额（优化版本）
     * @param int $itemId 报表项目ID
     * @param string $period 会计期间
     * @param object $column 列定义
     * @return float
     */
    private function calculateDirectAmountForColumnOptimized($itemId, $period, $column)
    {
        $amount = 0.00;

        // 从缓存获取该项目关联的科目
        $itemSubjects = $this->itemSubjectsCache[$itemId] ?? [];

        foreach ($itemSubjects as $itemSubject) {
            $subjectAmount = $this->getSubjectAmountForColumn(
                $itemSubject['subject_id'],
                $period,
                $itemSubject['balance_direction'],
                $column
            );

            // 根据运算类型进行加减
            if ($itemSubject['operation_type'] == 'add') {
                $amount += $subjectAmount * $itemSubject['weight'];
            } else {
                $amount -= $subjectAmount * $itemSubject['weight'];
            }
        }

        return $amount;
    }

    /**
     * 获取科目在指定列的金额
     * @param int $subjectId 科目ID
     * @param string $period 基准会计期间
     * @param string $balanceDirection 取数方向
     * @param object $column 列定义
     * @return float
     */
    private function getSubjectAmountForColumn($subjectId, $period, $balanceDirection, $column)
    {
        $periodType = $column->period_type;
        $balanceType = $column->balance_type;

        // 从缓存获取当前科目及所有下级科目的ID
        $subjectIds = $this->subjectTreeCache[$subjectId] ?? [$subjectId];

        $totalAmount = 0.00;

        foreach ($subjectIds as $currentSubjectId) {
            $amount = 0.00;

            if (in_array($periodType, ['current_year', 'last_year'])) {
                // 年度相关期间类型的处理
                $amount = $this->getSubjectAmountForYearPeriod($currentSubjectId, $period, $balanceDirection, $column);
            } else {
                // 月度期间类型的处理
                $amount = $this->getSubjectAmountForMonthPeriod($currentSubjectId, $period, $balanceDirection, $column);
            }

            $totalAmount += $amount;
        }

        return $totalAmount;
    }

    /**
     * 获取科目在年度期间的金额
     * @param int $subjectId 科目ID
     * @param string $period 基准会计期间
     * @param string $balanceDirection 取数方向
     * @param object $column 列定义
     * @return float
     */
    private function getSubjectAmountForYearPeriod($subjectId, $period, $balanceDirection, $column)
    {
        $periodType = $column->period_type;
        $balanceType = $column->balance_type;

        // 确定年度
        $year = substr($period, 0, 4);
        if ($periodType == 'last_year') {
            $year = intval($year) - 1;
        }

        // 获取年度第一期和最后一期
        $firstPeriod = $year . '-01';
        $lastPeriod = $this->getYearLastPeriod($year);

        switch ($balanceType) {
            case 'beginning':
                // 年度期初：年度第一期的期初余额
                return $this->getSubjectBalanceAmount($subjectId, $firstPeriod, $balanceDirection, 'beginning');

            case 'ending':
                // 年度期末：年度最后一期的期末余额
                return $this->getSubjectBalanceAmount($subjectId, $lastPeriod, $balanceDirection, 'ending');

            case 'period':
                // 年度本期发生：年度第一期到最后一期的累计发生额
                return $this->getSubjectYearPeriodAmount($subjectId, $firstPeriod, $lastPeriod, $balanceDirection);
        }

        return 0.00;
    }

    /**
     * 获取科目在月度期间的金额
     * @param int $subjectId 科目ID
     * @param string $period 基准会计期间
     * @param string $balanceDirection 取数方向
     * @param object $column 列定义
     * @return float
     */
    private function getSubjectAmountForMonthPeriod($subjectId, $period, $balanceDirection, $column)
    {
        $periodType = $column->period_type;
        $balanceType = $column->balance_type;

        // 确定实际期间
        $actualPeriod = $this->getActualPeriod($period, $periodType);

        return $this->getSubjectBalanceAmount($subjectId, $actualPeriod, $balanceDirection, $balanceType);
    }

    /**
     * 获取年度最后一期
     * @param string $year 年度
     * @return string
     */
    private function getYearLastPeriod($year)
    {
        // 查找该年度最大的期间记录
        $maxPeriod = $this->periodModel
            ->where('period', 'like', $year . '-%')
            ->order('period', 'desc')
            ->value('period');

        // 如果没有找到记录，默认返回12月
        return $maxPeriod ?: $year . '-12';
    }

    /**
     * 获取科目余额金额
     * @param int $subjectId 科目ID
     * @param string $period 会计期间
     * @param string $balanceDirection 取数方向
     * @param string $balanceType 余额类型
     * @return float
     */
    private function getSubjectBalanceAmount($subjectId, $period, $balanceDirection, $balanceType)
    {
        // 构建缓存键
        $cacheKey = $period . '_' . $balanceType;
        $balanceCache = $this->periodBalanceCache[$cacheKey] ?? [];

        // 从缓存获取科目的期间余额
        $balance = $balanceCache[$subjectId] ?? null;

        if (!$balance) {
            return 0.00;
        }

        $amount = 0.00;
        switch ($balanceType) {
            case 'beginning':
                // 期初余额
                if ($balanceDirection == 'balance') {
                    $amount = $balance['begin_balance'];
                } elseif ($balanceDirection == 'debit') {
                    $amount = $balance['begin_direction'] == '借' ? $balance['begin_balance'] : 0.00;
                } elseif ($balanceDirection == 'credit') {
                    $amount = $balance['begin_direction'] == '贷' ? $balance['begin_balance'] : 0.00;
                }
                break;

            case 'ending':
                // 期末余额
                if ($balanceDirection == 'balance') {
                    $amount = $balance['end_balance'];
                } elseif ($balanceDirection == 'debit') {
                    $amount = $balance['end_direction'] == '借' ? $balance['end_balance'] : 0.00;
                } elseif ($balanceDirection == 'credit') {
                    $amount = $balance['end_direction'] == '贷' ? $balance['end_balance'] : 0.00;
                }
                break;

            case 'period':
                // 本期发生额
                if ($balanceDirection == 'debit') {
                    $amount = $balance['debit_total'];
                } elseif ($balanceDirection == 'credit') {
                    $amount = $balance['credit_total'];
                } else {
                    $amount = $balance['debit_total'] - $balance['credit_total'];
                }
                break;
        }

        return $amount;
    }

    /**
     * 获取科目年度期间发生额
     * @param int $subjectId 科目ID
     * @param string $startPeriod 开始期间
     * @param string $endPeriod 结束期间
     * @param string $balanceDirection 取数方向
     * @return float
     */
    private function getSubjectYearPeriodAmount($subjectId, $startPeriod, $endPeriod, $balanceDirection)
    {
        $totalAmount = 0.00;

        // 获取年度范围内的所有期间
        $periods = $this->getPeriodsInRange($startPeriod, $endPeriod);

        foreach ($periods as $period) {
            $amount = $this->getSubjectBalanceAmount($subjectId, $period, $balanceDirection, 'period');
            $totalAmount += $amount;
        }

        return $totalAmount;
    }

    /**
     * 获取期间范围内的所有期间
     * @param string $startPeriod 开始期间
     * @param string $endPeriod 结束期间
     * @return array
     */
    private function getPeriodsInRange($startPeriod, $endPeriod)
    {
        $periods = [];
        $current = $startPeriod;

        while ($current <= $endPeriod) {
            $periods[] = $current;

            // 计算下一个期间
            $year = intval(substr($current, 0, 4));
            $month = intval(substr($current, 5, 2));

            $month++;
            if ($month > 12) {
                $month = 1;
                $year++;
            }

            $current = sprintf('%04d-%02d', $year, $month);
        }

        return $periods;
    }


    /**
     * 为指定列计算公式金额
     * @param string $formula 计算公式
     * @param array $reportData 已计算的报表数据
     * @param object $column 列定义
     * @return float
     */
    private function calculateFormulaAmountForColumn($formula, $reportData, $column)
    {
        if (empty($formula)) {
            return 0.00;
        }

        // 确定列键名
        $columnKey = $column->id === 'amount' ? 'amount' : 'column_' . $column->id;

        // 将行号替换为对应的金额
        $expression = $formula;

        // 创建行号到金额的映射
        $lineNumberMap = [];
        foreach ($reportData as $item) {
            if ($item['line_number'] > 0 && isset($item[$columnKey])) {
                $lineNumberMap[$item['line_number']] = $item[$columnKey];
            }
        }


        // 使用正则表达式精确替换完整的行号，避免部分匹配问题
        foreach ($lineNumberMap as $lineNumber => $amount) {
            // 使用单词边界确保只替换完整的数字
            $pattern = '/\b' . preg_quote((string)$lineNumber, '/') . '\b/';
            $expression = preg_replace($pattern, (string)$amount, $expression);
        }


        // 安全计算表达式
        try {
            // 只允许数字、运算符和括号
            if (preg_match('/^[0-9+\-*\/\.\(\)\s]+$/', $expression)) {
                return eval("return $expression;");
            }
        } catch (Exception $e) {
            // 计算错误时返回0
        }

        return 0.00;
    }

    /**
     * 根据期间类型获取实际期间
     * @param string $basePeriod 基准期间 (格式: YYYY-MM)
     * @param string $periodType 期间类型
     * @return string
     */
    private function getActualPeriod($basePeriod, $periodType)
    {
        switch ($periodType) {
            case 'current_month':
                return $basePeriod;

            case 'current_year':
                // 对于年度类型，这个方法不再使用，由新的年度处理方法处理
                return $basePeriod;

            case 'last_year':
                // 返回上年同期
                $year = intval(substr($basePeriod, 0, 4)) - 1;
                $month = substr($basePeriod, 5, 2);
                return $year . '-' . $month;

            case 'last_month':
                // 返回上期
                $year = intval(substr($basePeriod, 0, 4));
                $month = intval(substr($basePeriod, 5, 2));

                if ($month == 1) {
                    $year--;
                    $month = 12;
                } else {
                    $month--;
                }

                return sprintf('%04d-%02d', $year, $month);

            case 'custom':
            default:
                return $basePeriod;
        }
    }

    /**
     * 预处理模板数据（优化渲染性能，减少内存占用）
     * @param object $template 报表模板
     * @param array $reportData 报表数据
     * @param array $columns 动态列
     * @return array
     */
    private function prepareTemplateData($template, $reportData, $columns)
    {
        // 初始化模板数据
        $templateData = [
            'leftItems' => [],
            'rightItems' => [],
            'maxRows' => 0,
            'renderedRows' => []
        ];

        if ($this->isDoubleColumnReport($template->id)) {
            // 分离左右两侧数据并立即处理，避免重复存储
            $leftItems = [];
            $rightItems = [];

            foreach ($reportData as $item) {
                if ($item['column_position'] === 'left') {
                    $leftItems[] = $this->processItemForTemplate($item, $columns);
                } elseif ($item['column_position'] === 'right') {
                    $rightItems[] = $this->processItemForTemplate($item, $columns);
                }
            }

            $templateData['leftItems'] = $leftItems;
            $templateData['rightItems'] = $rightItems;
            $templateData['maxRows'] = max(count($leftItems), count($rightItems));

            // 预渲染行数据，减少模板中的循环处理
            $templateData['renderedRows'] = $this->preRenderBalanceSheetRows($leftItems, $rightItems, $columns);

            // 释放临时变量内存
            unset($leftItems, $rightItems, $reportData);

        } else {
            // 单栏报表处理
            $processedItems = [];
            foreach ($reportData as $item) {
                $processedItems[] = $this->processItemForTemplate($item, $columns);
            }
            $templateData['items'] = $processedItems;
            unset($processedItems, $reportData);
        }

        return $templateData;
    }

    /**
     * 处理单个报表项目数据
     * @param array $item 报表项目
     * @param array $columns 动态列
     * @return array
     */
    private function processItemForTemplate($item, $columns)
    {
        $processedItem = [
            'id' => $item['id'],
            'item_name' => $item['item_name'],
            'line_number' => $item['line_number'] ?: '',
            'type' => $item['type'],
            'calculation_type' => $item['calculation_type'],
            'bold' => $item['bold'],
            'indent' => $item['indent'],
            'indent_html' => str_repeat('&nbsp;', $item['indent'] * 4),
            'columns' => []
        ];

        // 预处理列数据
        foreach ($columns as $column) {
            $columnKey = 'column_' . $column->id;
            $amount = isset($item[$columnKey]) ? $item[$columnKey] : 0;
            $processedItem['columns'][$column->id] = number_format($amount, $column->decimal_places);
        }

        return $processedItem;
    }

    /**
     * 判断报表是否为双栏报表
     * @param int $templateId 报表模板ID
     * @return bool
     */
    private function isDoubleColumnReport($templateId)
    {
        // 获取该模板下所有报表项目的column_position
        $columnPositions = $this->reportItemModel
            ->where('template_id', $templateId)
            ->where('status', 1)
            ->where('column_position', 'neq', 'single')
            ->count();

        return $columnPositions > 0;
    }

    /**
     * 预渲染资产负债表行数据
     * @param array $leftItems 左侧项目
     * @param array $rightItems 右侧项目
     * @param array $columns 动态列
     * @return array
     */
    private function preRenderBalanceSheetRows($leftItems, $rightItems, $columns)
    {
        $renderedRows = [];
        $maxRows = max(count($leftItems), count($rightItems));

        for ($i = 0; $i < $maxRows; $i++) {
            $leftItem = isset($leftItems[$i]) ? $leftItems[$i] : null;
            $rightItem = isset($rightItems[$i]) ? $rightItems[$i] : null;

            $renderedRows[] = [
                'left' => $leftItem,
                'right' => $rightItem
            ];
        }

        return $renderedRows;
    }

}
