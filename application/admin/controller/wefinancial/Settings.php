<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

class Settings extends Backend
{

    public function _initialize()
    {
        parent::_initialize();

        // 获取当前配置
        $addonConfig = get_addon_config('wefinancial');
        $this->view->assign('addonConfig', $addonConfig);

        // 检查是否已有期间记录，用于控制启用期间字段的可编辑性
        $periodModel = new \app\admin\model\wefinancial\Period();
        $hasPeriodRecords = $periodModel->hasAnyPeriodRecords();
        $this->view->assign('hasPeriodRecords', $hasPeriodRecords);

        // 如果已有期间记录，获取最早期间用于显示
        if ($hasPeriodRecords) {
            $earliestPeriod = $periodModel->getEarliestPeriod();
            $this->view->assign('earliestPeriod', $earliestPeriod);
        }

        // 获取配置选项
        $this->view->assign('taxTypeList', [
            '1' => '一般纳税人',
            '2' => '小规模纳税人'
        ]);

        // 获取会计制度选项（从插件配置中读取）
        $accountingSystemList = $this->getAccountingSystemList();
        $this->view->assign('accountingSystemList', $accountingSystemList);
    }

    /**
     * 初始化设置页面
     */
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 保存初始化设置
     */
    public function save()
    {
        $params = $this->request->post();

        // 检查是否已有期间记录，区分首次初始化和后续修改
        $periodModel = new \app\admin\model\wefinancial\Period();
        $currentConfig = get_addon_config('wefinancial');
        $hasPeriodRecords = $periodModel->hasAnyPeriodRecords();

        if ($hasPeriodRecords) {
            // 已初始化场景：只验证可编辑的企业信息字段
            $this->validateCompanyInfo($params);

            // 使用当前配置中的启用期间和会计制度，防止前端篡改
            $params['initial_period'] = $currentConfig['initial_period'] ?? '';
            $params['accounting_system'] = $currentConfig['accounting_system'] ?? '';

            // 验证这些关键字段确实没有被修改（双重保护）
            if (empty($params['initial_period']) || empty($params['accounting_system'])) {
                $this->error('系统配置异常，请联系管理员');
            }
        } else {
            // 首次初始化场景：验证所有必填字段
            $this->validateInitialSetup($params);
        }

        try {
            // 开启事务
            Db::startTrans();

            // 保存配置
            $configData = [
                'initial_period' => $params['initial_period'],
                'company_name' => $params['company_name'],
                'tax_name' => $params['tax_name'],
                'tax_no' => $params['tax_no'],
                'tax_type' => $params['tax_type'],
                'accounting_system' => $params['accounting_system']
            ];

            set_addon_config('wefinancial', $configData);

            // 构建成功消息
            if ($hasPeriodRecords) {
                // 已初始化场景：只是更新企业信息
                $successMessage = '企业信息更新成功！';
            } else {
                // 首次初始化场景：创建期间记录并导入科目
                $periodModel->createInitialPeriod($params['initial_period']);

                // 根据选择的会计制度导入科目
                $importResult = $this->importSubjectsByAccountingSystem($params['accounting_system']);

                $successMessage = '初始化设置保存成功！已自动创建会计期间记录。';
                if ($importResult['success']) {
                    $successMessage .= $importResult['message'];
                } else {
                    // 如果科目导入失败，记录警告但不阻止整个流程
                    $successMessage .= '科目导入出现问题：' . $importResult['message'];
                }
            }

            Db::commit();

        } catch (Exception $e) {
            Db::rollback();
            $this->error('保存失败：' . $e->getMessage());
        }
        $this->success($successMessage);
    }

    /**
     * 重置初始化设置 - 危险操作，清空所有业务数据
     */
    public function reset()
    {
        if (!$this->request->isPost()) {
            $this->error('非法请求');
        }

        $confirm = $this->request->post('confirm', '');
        if ($confirm !== 'CONFIRM_RESET') {
            $this->error('请确认重置操作');
        }

        try {
            // 开启事务
            Db::startTrans();

            // 1. 清空凭证相关数据
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_voucher_entry');
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_voucher');

            // 2. 清空期间数据
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_period');

            // 3. 清空期间余额数据
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_period_balance');

            // 4. 清空科目、科目类型
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_subject');
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_subject_type');

            //5. 清空报表相关数据
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_report_item_subject');
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_report_item');
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_report_column');
            Db::execute('TRUNCATE TABLE ' . config('database.prefix') . 'wefinancial_report_template');

            // 5. 重置配置为默认值
            $defaultConfig = [
                'initial_period' => date('Y-m'),
                'company_name' => '',
                'tax_name' => '',
                'tax_no' => '',
                'tax_type' => '1',
                'accounting_system' => 'small_enterprise'
            ];

            set_addon_config('wefinancial', $defaultConfig);

            // 提交事务
            Db::commit();

            $this->success('系统重置成功！所有业务数据已清空，配置已恢复默认值');

        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('重置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取重置确认信息
     */
    public function getResetInfo()
    {
        if (!$this->request->isAjax()) {
            $this->error('非法请求');
        }

        try {
            // 统计将要清空的数据量
            $voucherCount = Db::name('wefinancial_voucher')->count();
            $entryCount = Db::name('wefinancial_voucher_entry')->count();
            $periodCount = Db::name('wefinancial_period')->count();
            $balanceCount = Db::name('wefinancial_period_balance')->count();
            $userSubjectCount = Db::name('wefinancial_subject')->count();

            $info = [
                'voucher_count' => $voucherCount,
                'entry_count' => $entryCount,
                'period_count' => $periodCount,
                'balance_count' => $balanceCount,
                'user_subject_count' => $userSubjectCount,
                'total_records' => $voucherCount + $entryCount + $periodCount + $balanceCount + $userSubjectCount
            ];

            $this->success('获取成功', null, $info);

        } catch (Exception $e) {
            $this->error('获取信息失败：' . $e->getMessage());
        }
    }

    /**
     * 根据会计制度导入科目和执行SQL脚本
     * @param string $accountingSystem 会计制度代码
     * @return array 返回结果 ['success' => bool, 'message' => string]
     */
    private function importSubjectsByAccountingSystem($accountingSystem)
    {
        // 获取会计制度列表映射
        $accountingSystemList = $this->getAccountingSystemList();

        // 检查会计制度是否存在
        if (!isset($accountingSystemList[$accountingSystem])) {
            return ['success' => false, 'message' => '不支持的会计制度：' . $accountingSystem];
        }

        $systemName = $accountingSystemList[$accountingSystem];
        $systemDir = ROOT_PATH . 'addons/wefinancial/init/' . $systemName . '/';

        // 构建Excel文件路径
        $fileName = $systemName . '.xlsx';
        $filePath = $systemDir . $fileName;

        // 检查Excel文件是否存在
        if (!file_exists($filePath)) {
            return ['success' => false, 'message' => '会计制度文件不存在：' . $fileName];
        }

        try {
            // 开启事务
            Db::startTrans();

            // 1. 导入Excel科目数据
            $subjectModel = new \app\admin\model\wefinancial\Subject();
            $result = $subjectModel->importFromExcel($filePath);

            if (!$result['success']) {
                Db::rollback();
                return ['success' => false, 'message' => $result['message']];
            }

            // 2. 执行目录下的所有SQL脚本
            $sqlResult = $this->executeSqlScripts($systemDir);

            if (!$sqlResult['success']) {
                Db::rollback();
                return ['success' => false, 'message' => '科目导入成功，但SQL脚本执行失败：' . $sqlResult['message']];
            }

            Db::commit();

            // 构建成功消息
            $message = '科目导入成功（' . $systemName . '）。';
            if ($sqlResult['count'] > 0) {
                $message .= '已执行 ' . $sqlResult['count'] . ' 个SQL脚本。';
            }

            return ['success' => true, 'message' => $message];

        } catch (\Exception $e) {
            Db::rollback();
            return ['success' => false, 'message' => '导入过程中发生错误：' . $e->getMessage()];
        }
    }

    /**
     * 执行指定目录下的所有SQL脚本
     * @param string $directory 目录路径
     * @return array 返回结果 ['success' => bool, 'message' => string, 'count' => int]
     */
    private function executeSqlScripts($directory)
    {
        if (!is_dir($directory)) {
            return ['success' => false, 'message' => '目录不存在：' . $directory, 'count' => 0];
        }

        try {
            // 获取目录下所有.sql文件
            $sqlFiles = glob($directory . '*.sql');

            if (empty($sqlFiles)) {
                return ['success' => true, 'message' => '没有找到SQL脚本文件', 'count' => 0];
            }

            // 按文件名排序，确保执行顺序
            sort($sqlFiles);

            $executedCount = 0;
            $errors = [];

            foreach ($sqlFiles as $sqlFile) {
                $fileName = basename($sqlFile);

                try {
                    // 读取SQL文件内容
                    $sqlContent = file_get_contents($sqlFile);

                    if (empty(trim($sqlContent))) {
                        continue; // 跳过空文件
                    }

                    // 执行SQL脚本
                    // 注意：这里使用原生SQL执行，因为可能包含多条语句
                    $this->executeSqlFile($sqlContent, $fileName);

                    $executedCount++;

                } catch (\Exception $e) {
                    $errors[] = $fileName . ': ' . $e->getMessage();
                }
            }

            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => '部分SQL脚本执行失败：' . implode('; ', $errors),
                    'count' => $executedCount
                ];
            }

            return [
                'success' => true,
                'message' => '所有SQL脚本执行成功',
                'count' => $executedCount
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'SQL脚本执行过程中发生错误：' . $e->getMessage(), 'count' => 0];
        }
    }

    /**
     * 执行单个SQL文件内容
     * @param string $sqlContent SQL内容
     * @param string $fileName 文件名（用于错误提示）
     * @throws \Exception
     */
    private function executeSqlFile($sqlContent, $fileName)
    {
        try {
            // 获取PDO连接
            $pdo = Db::connect()->getPdo();

            // 清理SQL内容，移除注释但保持语句完整性
            $cleanedSql = $this->cleanSqlContent($sqlContent);

            if (empty(trim($cleanedSql))) {
                return; // 跳过空文件
            }

            // 使用PDO的exec方法执行整个SQL文件
            // 这样可以保持SQL语句之间的上下文关系（如LAST_INSERT_ID()、变量等）
            $result = $pdo->exec($cleanedSql);

            // 检查是否有错误
            $errorInfo = $pdo->errorInfo();
            if ($errorInfo[0] !== '00000') {
                throw new \Exception("PDO错误: " . $errorInfo[2]);
            }

        } catch (\PDOException $e) {
            throw new \Exception("执行SQL文件失败 (文件: {$fileName}): " . $e->getMessage());
        } catch (\Exception $e) {
            throw new \Exception("执行SQL文件失败 (文件: {$fileName}): " . $e->getMessage());
        }
    }

    /**
     * 清理SQL内容，移除注释但保持语句完整性
     * @param string $sqlContent SQL内容
     * @return string 清理后的SQL内容
     */
    private function cleanSqlContent($sqlContent)
    {
        // 移除单行注释（-- 开头的行）
        $sqlContent = preg_replace('/--.*$/m', '', $sqlContent);

        // 移除多行注释（/* ... */）
        $sqlContent = preg_replace('/\/\*.*?\*\//s', '', $sqlContent);

        // 移除多余的空行，但保持必要的换行符
        $sqlContent = preg_replace('/\n\s*\n/', "\n", $sqlContent);

        // 去除首尾空白
        $sqlContent = trim($sqlContent);

        return $sqlContent;
    }

    /**
     * 获取会计制度选项列表
     * @return array
     */
    private function getAccountingSystemList()
    {
        // 从插件配置中获取会计制度选项
        $configPath = ADDON_PATH . 'wefinancial' . DS . 'config.php';
        if (file_exists($configPath)) {
            $config = include $configPath;
            foreach ($config as $item) {
                if ($item['name'] === 'accounting_system' && isset($item['content'])) {
                    return $item['content'];
                }
            }
        }

        // 如果配置文件不存在或没有找到配置项，返回默认选项
        return [
            'small_enterprise_2013' => '小企业会计准则（2013年颁）',
            'new_accounting_2019_old' => '新会计准则（2019年未执行新收入、新金融准则）',
            'new_accounting_2019_new' => '新会计准则（2019年执行新金融、新收入和新租赁准则）',
            'new_accounting_2006' => '新会计准则（2006年颁）',
        ];
    }

    /**
     * 验证首次初始化设置的所有必填字段
     * @param array $params 提交的参数
     * @throws Exception
     */
    private function validateInitialSetup($params)
    {
        // 验证必填字段
        $required = ['initial_period', 'company_name', 'tax_name', 'tax_no', 'tax_type', 'accounting_system'];
        foreach ($required as $field) {
            if (empty($params[$field])) {
                $this->error('请填写完整的初始化信息');
            }
        }

        // 验证期间格式
        if (!preg_match('/^\d{4}-\d{2}$/', $params['initial_period'])) {
            $this->error('启用期间格式错误，请使用YYYY-MM格式');
        }

        // 验证纳税人识别号格式（简单验证）
        if (strlen($params['tax_no']) < 15) {
            $this->error('纳税人识别号格式错误');
        }
    }

    /**
     * 验证企业信息字段（用于已初始化后的修改）
     * @param array $params 提交的参数
     * @throws Exception
     */
    private function validateCompanyInfo($params)
    {
        // 验证企业信息必填字段
        $required = ['company_name', 'tax_name', 'tax_no', 'tax_type'];
        foreach ($required as $field) {
            if (empty($params[$field])) {
                $this->error('请填写完整的企业信息');
            }
        }

        // 验证纳税人识别号格式（简单验证）
        if (strlen($params['tax_no']) < 15) {
            $this->error('纳税人识别号格式错误');
        }
    }
}
